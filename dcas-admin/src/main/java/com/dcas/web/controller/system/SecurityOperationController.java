package com.dcas.web.controller.system;

import com.dcas.common.annotation.Log;
import com.dcas.common.core.domain.R;
import com.dcas.common.domain.entity.SecurityTemplate;
import com.dcas.common.enums.BusinessType;
import com.dcas.common.exception.file.InvalidExtensionException;
import com.dcas.common.model.dto.CommonDto;
import com.dcas.common.model.dto.SecurityContentDTO;
import com.dcas.common.model.dto.SecurityProduceDTO;
import com.dcas.common.model.dto.TemplateQueryDTO;
import com.dcas.common.model.param.SecurityEditParam;
import com.dcas.common.model.param.SecurityWorkParam;
import com.dcas.common.model.req.IdsReq;
import com.dcas.common.model.req.SecurityReportReq;
import com.dcas.common.model.req.SecuritySearchReq;
import com.dcas.common.model.vo.*;
import com.dcas.common.utils.PageResult;
import com.dcas.system.service.CoLicenseService;
import com.dcas.system.service.CommonService;
import com.dcas.system.service.SecurityOfflineService;
import com.dcas.system.service.SecurityOperationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/7/3 11:06
 * @since 1.4.0
 */
@RestController
@Api(tags = "安全作业管理")
@RequiredArgsConstructor
@RequestMapping("/api/security")
public class SecurityOperationController {

    private final SecurityOperationService securityOperationService;
    private final SecurityOfflineService securityOfflineService;

    @PostMapping
    @ApiOperation(value = "创建安全作业")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "创建安全作业", businessType = BusinessType.INSERT, module = "检查作业")
    public R<Boolean> create(@Validated @RequestBody SecurityWorkParam param) {
        Integer id = securityOperationService.create(param);
        securityOperationService.produce(new SecurityProduceDTO(id.toString(), param.getTemplateId(), param.getServiceContent(), param.getBusSystem()));
        return R.success();
    }

    @GetMapping
    @ApiOperation(value = "分页查询安全作业")
    public R<PageResult<SecurityWorkVO>> pageQuery(SecuritySearchReq req) {
        return R.success(securityOperationService.pageQuery(req));
    }

    @PutMapping
    @ApiOperation(value = "编辑安全作业")
    @Log(title = "编辑安全作业", businessType = BusinessType.UPDATE, module = "检查作业")
    public R<Boolean> edit(@Validated @RequestBody SecurityEditParam param) {
        securityOperationService.edit(param);
        return R.success();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除安全作业")
    @Log(title = "删除安全作业", businessType = BusinessType.DELETE, module = "检查作业")
    public R<Boolean> delete(@Validated @RequestBody IdsReq req) {
        securityOperationService.delete(req);
        return R.success();
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "执行安全作业")
    @Log(title = "执行安全作业", businessType = BusinessType.EXECUTE, module = "检查作业")
    public R<List<SecurityLabelVO>> execute(@PathVariable("id") Integer id) {
        return R.success(securityOperationService.execute(id));
    }

    @GetMapping("/content")
    @ApiOperation(value = "获取安全作业调研内容")
    public R<List<SecurityContentVO>> content(@ApiParam("安全作业id") @RequestParam("id") Integer id,
                                              @ApiParam("检查类别名称") @RequestParam("categoryName") String categoryName) {
        return R.success(securityOperationService.content(id, categoryName));
    }

    @PutMapping("/content")
    @ApiOperation(value = "保存安全作业调研内容")
    @Log(title = "保存安全作业调研内容", businessType = BusinessType.INSERT, module = "检查作业")
    public R<Boolean> saveContent(@Validated @RequestBody SecurityContentDTO dto) {
        securityOperationService.saveContent(dto);
        return R.success();
    }

    @PutMapping("/progress")
    @ApiOperation(value = "编辑安全作业进度")
    @Log(title = "编辑安全作业进度", businessType = BusinessType.UPDATE, module = "检查作业")
    public R<Boolean> editProgress(@Validated @RequestBody SecurityContentDTO dto) {
        securityOperationService.editProgress(dto);
        return R.success();
    }

    @PutMapping("/status")
    @ApiOperation(value = "编辑安全作业状态")
    @Log(title = "编辑安全作业状态", businessType = BusinessType.UPDATE, module = "检查作业")
    public R<Boolean> editStatus(Integer securityId) {
        securityOperationService.editStatus(securityId);
        return R.success();
    }

    @PostMapping("/template")
    @ApiOperation(value = "获取安全检查模板")
    public R<List<SecurityTemplate>> getTemplateList(@Validated @RequestBody TemplateQueryDTO dto) {
        return R.success(securityOperationService.getSecurityTemplateList(dto));
    }

    @PostMapping("/export/word")
    @ApiOperation(value = "安全检查作业下载")
    @Log(title = "安全检查作业下载", businessType = BusinessType.EXPORT, module = "检查作业")
    public void exportWord(HttpServletResponse response, @Validated @RequestBody SecurityReportReq req) {
        securityOperationService.exportWord(response, req);
    }

    @GetMapping("/file/download")
    @ApiOperation(value = "导出文件压缩包")
    @Log(title = "导出文件压缩包", businessType = BusinessType.EXPORT, module = "检查作业")
    public void fileDownload(Integer id, HttpServletResponse response) {
        securityOperationService.fileDownload(id, response);
    }

    @GetMapping("/content/file")
    @ApiOperation(value = "获取安全作业检查内容附件")
    public R<List<ContentFileVO>> contentFile(@ApiParam("检查作业id") @RequestParam Integer id,
                                              @ApiParam("检查类别名称") @RequestParam String categoryName) {
        return R.success(securityOperationService.contentFile(id, categoryName));
    }

    @PostMapping("/content/file")
    @ApiOperation(value = "上传检查内容附件")
    @Log(title = "上传检查内容附件", businessType = BusinessType.IMPORT, module = "检查作业")
    public R<Map<String, Object>> uploadContentFile(@ApiParam("检查内容id") @RequestParam Integer contentId,
                                                    MultipartFile file) throws IOException, InvalidExtensionException {
        return R.success(securityOperationService.uploadContentFile(contentId, file));
    }

    @DeleteMapping("/content/file")
    @ApiOperation(value = "删除检查内容附件")
    @Log(title = "删除检查内容附件", businessType = BusinessType.DELETE, module = "检查作业")
    public R<Boolean> deleteContentFile(@RequestParam Integer contentId, @RequestParam String fileId) {
        securityOperationService.deleteContentFile(contentId, fileId);
        return R.success();
    }

    @GetMapping("/content/result")
    @ApiOperation(value = "检查类别结果")
    public R<SecurityCategoryResultVO> result(@ApiParam("安全作业id") @RequestParam Integer operationId,
                                              @ApiParam("检查类别名称，传类别名称返回具体类别得分详情；不传返回作业得分详情") String categoryName) {
        return R.success(securityOperationService.result(operationId, categoryName));
    }

    @GetMapping("/detail")
    @ApiOperation(value = "安全作业详情")
    @Log(title = "安全作业详情", businessType = BusinessType.QUERY, module = "检查作业")
    public R<SecurityDetailVO> detail(@ApiParam("安全作业id") @RequestParam Integer id) {
        return R.success(securityOperationService.detail(id));
    }

    @GetMapping("/flush")
    @ApiOperation(value = "刷新安全检测结果")
    public R<Boolean> flushBasic(@RequestParam Integer id) {
        securityOperationService.flushBasic(id);
        return R.success();
    }


    @GetMapping("/serviceContent/list")
    @ApiOperation(value = "获取所有检查内容")
    public R<List<Map<Integer, String>>> listServiceContent() {
        return R.success(securityOperationService.listServiceContent());
    }

    @GetMapping("/pdf/download")
    @ApiOperation(value = "下载前置说明")
    @Log(title = "下载前置说明", businessType = BusinessType.EXPORT, module = "检查作业")
    public Object pdfDownload(Integer templateId, HttpServletRequest request) {
        return securityOperationService.pdfDownload(templateId, request);
    }

    @GetMapping("/export/report/precheck")
    @ApiOperation(value = "导出报告前校验")
    @PreAuthorize("@ss.hasPermi('security:exportReport')")
    public void preCheck() {

    }

    @GetMapping("/offline")
    @ApiOperation(value = "离线问卷模板导出")
    @Log(title = "离线问卷模板导出", businessType = BusinessType.EXPORT, module = "检查作业")
    public void offlineQuestionnaireExport(@RequestParam String operationId, HttpServletResponse response) {
        securityOfflineService.offlineQuestionnaireExport(operationId, response);
    }

    @PostMapping("/offline")
    @ApiOperation("离线问卷导入")
    @Log(title = "离线问卷导入", businessType = BusinessType.IMPORT, module = "检查作业")
    public R<Boolean> offlineQuestionnaireImport(@RequestParam String operationId, MultipartFile file) {
        securityOfflineService.offlineQuestionnaireImport(operationId, file);
        return R.success();
    }

    @GetMapping("/online")
    @ApiOperation("查询在线问卷")
    public R<OnlineSecurityContentVO> selectOnlineQuestionnaire(@RequestParam String operationId, @RequestParam Integer jobType) {
        return R.success(securityOfflineService.selectOnlineQuestionnaire(operationId, jobType));
    }

    @PostMapping("/online/save")
    @ApiOperation("保存在线问卷")
    @Log(title = "保存在线问卷", businessType = BusinessType.INSERT, module = "检查作业")
    public R<Boolean> saveOnlineQuestionnaire(@Validated @RequestBody OnlineSecurityContentVO vo) {
        securityOfflineService.saveOnlineQuestionnaire(vo);
        return R.success();
    }

    @GetMapping("/online/import")
    @ApiOperation("在线问卷结果导入")
    @Log(title = "在线问卷结果导入", businessType = BusinessType.IMPORT, module = "检查作业")
    public R<Boolean> onlineQuestionnaireImport(@RequestParam String operationId) {
        securityOfflineService.onlineQuestionnaireImport(operationId);
        return R.success();
    }
}
