package com.dcas.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcas.common.domain.entity.ApiRelease;
import com.dcas.common.model.vo.ApiReleaseVO;
import com.dcas.common.model.vo.IntegrationFormFieldVO;
import com.dcas.common.model.vo.IntegrationInterfaceVO;
import com.dcas.common.model.vo.ProductCapabilityTreeVO;

import java.util.List;

/**
 * API版本记录服务接口
 *
 * <AUTHOR>
 */
public interface IApiReleaseService extends IService<ApiRelease> {
    
    /**
     * 查询产品能力树形结构
     *
     * @return 产品能力树形结构列表
     */
    List<ProductCapabilityTreeVO> getProductCapabilityTree();

    List<IntegrationInterfaceVO> getProductsByReleaseId(Long productId, Integer taskType);

    List<ApiReleaseVO> queryApiReleaseList();

    List<IntegrationFormFieldVO> getProductHeader(Long releaseId);
}
