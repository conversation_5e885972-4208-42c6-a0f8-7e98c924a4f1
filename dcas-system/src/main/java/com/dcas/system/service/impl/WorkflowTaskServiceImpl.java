package com.dcas.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.domain.entity.*;
import com.dcas.common.enums.JobStatusEnum;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.mapper.ApiIntegratedProductMapper;
import com.dcas.common.mapper.ApiInterfaceMapper;
import com.dcas.common.mapper.TaskStepMapper;
import com.dcas.common.mapper.WorkflowTaskMapper;
import com.dcas.common.model.dto.StepMsgDTO;
import com.dcas.common.model.req.IdsReq;
import com.dcas.common.model.req.WorkflowTaskReq;
import com.dcas.common.model.req.WorkflowTaskUpdateReq;
import com.dcas.common.model.vo.IntegrationInterfaceVO;
import com.dcas.common.model.vo.WorkflowDetailVO;
import com.dcas.common.model.vo.WorkflowTaskDetailVO;
import com.dcas.common.utils.*;
import com.dcas.system.service.IWorkflowTaskService;
import com.dcas.system.service.ITaskExecutionService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 工作流任务Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkflowTaskServiceImpl extends ServiceImpl<WorkflowTaskMapper, WorkflowTask> implements IWorkflowTaskService {

    private final TaskStepServiceImpl taskStepService;
    private final ITaskExecutionService taskExecutionService;
    private final ApiIntegratedProductMapper apiIntegratedProductMapper;

    // 用于存储正在执行的任务，支持任务终止
    private final ConcurrentHashMap<Long, CompletableFuture<Void>> runningTasks = new ConcurrentHashMap<>();
    
    // 用于存储任务终止标志
    private final ConcurrentHashMap<Long, Boolean> terminationFlags = new ConcurrentHashMap<>();
    private final ApiInterfaceMapper apiInterfaceMapper;
    private final TaskStepMapper taskStepMapper;

    /**
     * 查询工作流任务
     * 
     * @param taskId 工作流任务主键
     * @return 工作流任务
     */
    @Override
    @SchemaSwitch
    public WorkflowTaskDetailVO selectWorkflowTaskByTaskId(Long taskId) {
        return baseMapper.selectDetail(taskId);
    }

    /**
     * 查询工作流任务列表
     * 
     * @param req 工作流任务
     * @return 工作流任务
     */
    @Override
    public PageResult<WorkflowTask> selectWorkflowTaskList(WorkflowTaskReq req) {
        try (Page<?> page = PageHelper.startPage(req.getCurrentPage(), req.getPageSize())) {
            List<WorkflowTask> workflowTasks = baseMapper.selectWorkflowTaskList(req);
            return PageResult.ofPage(page.getTotal(), workflowTasks);
        }
    }

    /**
     * 新增工作流任务
     * 
     * @param workflowTask 工作流任务
     */
    @Override
    @SchemaSwitch
    public void insertWorkflowTask(WorkflowTask workflowTask) {
        Func.beforeInsert(workflowTask);
        workflowTask.setStatus(JobStatusEnum.UN_START.getCode());
        workflowTask.setProgressPercentage(0);
        ApiIntegratedProduct apiIntegratedProduct = apiIntegratedProductMapper.selectById(workflowTask.getProductId());
        workflowTask.setCapability(apiIntegratedProduct.getCapability());
        workflowTask.setLogo(apiIntegratedProduct.getLogo());
        workflowTask.setProductName(apiIntegratedProduct.getProductName());
        workflowTask.setCompany(apiIntegratedProduct.getCompany());
        baseMapper.insert(workflowTask);
        List<ApiInterface> apiInterfaces = apiInterfaceMapper.selectByReleaseIdAndTaskType(apiIntegratedProduct.getReleaseId(), workflowTask.getTaskType());
        createWorkflowTaskWithSteps(workflowTask, apiIntegratedProduct, apiInterfaces);
    }

    /**
     * 修改工作流任务
     * 
     * @param req 工作流任务
     */
    @Override
    public void updateWorkflowTask(WorkflowTaskUpdateReq req) {
        WorkflowTask workflowTask = new WorkflowTask();
        BeanUtil.copyProperties(req, workflowTask);
        baseMapper.updateById(workflowTask);
    }

    /**
     * 批量删除工作流任务
     *
     */
    @Override
    @Transactional
    public void deleteWorkflowTaskByTaskIds(IdsReq req) {
        for (Integer taskId : req.getIds()) {
            // 先删除相关的步骤和执行记录
            taskStepService.deleteTaskStepByTaskId(Long.parseLong(taskId.toString()));
            taskExecutionService.deleteTaskExecutionByTaskId(Long.parseLong(taskId.toString()));
        }
        baseMapper.deleteBatchIds(req.getIds());
    }

    /**
     * 删除工作流任务信息
     * 
     * @param taskId 工作流任务主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteWorkflowTaskByTaskId(Long taskId) {
        // 先删除相关的步骤和执行记录
        taskStepService.deleteTaskStepByTaskId(taskId);
        taskExecutionService.deleteTaskExecutionByTaskId(taskId);
        return baseMapper.deleteWorkflowTaskByTaskId(taskId);
    }

    /**
     * 创建完整的工作流步骤
     *
     */
    private void createWorkflowTaskWithSteps(WorkflowTask workflowTask, ApiIntegratedProduct apiIntegratedProduct, List<ApiInterface> apiInterfaces) {
        // 创建任务
        if (CollUtil.isNotEmpty(apiInterfaces)) {
            List<TaskStep> result = new ArrayList<>();
            // 设置步骤的任务ID和默认值
            for (int i = 0; i < apiInterfaces.size(); i++) {
                TaskStep step = new TaskStep();
                ApiInterface apiInterface = apiInterfaces.get(i);
                step.setTaskId(workflowTask.getId());
                step.setInterfaceId(apiInterface.getId());
                step.setStepOrder(i + 1);
                step.setStepName(apiInterface.getApiName());
                step.setStatus(JobStatusEnum.UN_START.getCode());
                step.setApiEndpoint(apiIntegratedProduct.getUrl() + apiInterfaces.get(i).getApiPath());
                step.setHttpMethod(apiInterface.getApiMethod());
                step.setRequestHeaders(apiIntegratedProduct.getConfigParams());
                step.setRequestParams(apiInterface.getReqParams());
                step.setResponseFormat(apiInterface.getReqResult());
                step.setTimeoutSeconds(30);
                step.setRetryCount(0);
                step.setMaxRetries(0);
                step.setRequired(1); // 默认必须成功
                step.setCreateTime(DateUtils.getNowDate());
                step.setCreateBy(getUsername());
                step.setContinueOnFailure(0);
                step.setStartTime(DateUtils.getNowDate());
                result.add(step);
            }
            
            // 批量插入步骤
            taskStepService.saveBatch(result);
            
            // 更新任务的总步骤数
            workflowTask.setTotalSteps(result.size());
            workflowTask.setCurrentStep(0);
            WorkflowTask update = new WorkflowTask();
            update.setId(workflowTask.getId());
            update.setTotalSteps(result.size());
            update.setCurrentStep(0);
            baseMapper.updateById(update);
        }
    }

    /**
     * 异步执行工作流任务
     * 
     * @param taskId 任务ID
     * @return CompletableFuture
     */
    @Override
    public CompletableFuture<Void> executeTaskAsync(Long taskId) {
        // 如果任务正在执行中，忽略
        if (runningTasks.containsKey(taskId)) {
            log.warn("任务正在执行中，忽略，任务ID: {}", taskId);
            return CompletableFuture.completedFuture(null);
        }
        // 执行或者重启时检查是否需要清理任务上下文，除第一次执行都需要重置任务状态和错误信息等内容
        restartTask(taskId);
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            try {
                executeTask(taskId);
            } catch (Exception e) {
                log.error("异步执行任务失败，任务ID: {}", taskId, e);
                recordTaskError(taskId, "异步执行异常: " + e.getMessage());
                updateTaskStatus(taskId, JobStatusEnum.EXCEPTION_STOP.getCode());
            }
        });
        
        runningTasks.put(taskId, future);
        return future;
    }

    /**
     * 同步执行工作流任务
     * 
     * @param taskId 任务ID
     */
    @Override
    public void executeTask(Long taskId) {
        log.info("开始执行工作流任务，任务ID: {}", taskId);
        
        try {
            /*// 检查任务是否可以执行
            if (!canExecuteTask(taskId)) {
                log.warn("任务不能执行，任务ID: {}", taskId);
            }*/
            
            WorkflowTask task = baseMapper.selectById(taskId);
            if (task == null) {
                log.error("任务不存在，任务ID: {}", taskId);
                throw new ServiceException("任务不存在");
            }
            
            // 创建执行记录
            TaskExecution execution = taskExecutionService.createTaskExecution(
                taskId, getUserId(), getUsername());
            
            // 开始执行
            Date startTime = DateUtils.getNowDate();
            updateTaskStatus(taskId, JobStatusEnum.RUNNING.getCode());
            baseMapper.updateWorkflowTaskExecutionTime(taskId, startTime, null, null, getUsername());
            taskExecutionService.startExecution(execution.getExecutionId(), task.getTotalSteps());
            
            // 执行任务步骤
            StepMsgDTO stepMsg = executeTaskSteps(task, execution.getExecutionId());
            
            // 完成执行
            Date endTime = DateUtils.getNowDate();
            long duration = endTime.getTime() - startTime.getTime();
            baseMapper.updateWorkflowTaskExecutionTime(taskId, startTime, endTime, duration, getUsername());

            if (stepMsg.getSuccess()) {
                updateTaskStatus(taskId, JobStatusEnum.COMPLETE.getCode());
                // 验证是否具备产品能力
                String verifyResult = verifyTaskResult(stepMsg.getMessage());
                baseMapper.recordTaskComplete(taskId, stepMsg.getMessage(), verifyResult);
                taskExecutionService.completeExecution(execution.getExecutionId(), stepMsg.getMessage());
                log.info("工作流任务执行成功，任务ID: {}", taskId);
            } else {
                updateTaskStatus(taskId, JobStatusEnum.EXCEPTION_STOP.getCode());
                recordTaskError(taskId, stepMsg.getMessage());
                taskExecutionService.failExecution(execution.getExecutionId(), stepMsg.getMessage());
                log.warn("工作流任务执行失败，任务ID: {}", taskId);
            }
        } catch (Exception e) {
            log.error("执行工作流任务异常，任务ID: {}", taskId, e);
            updateTaskStatus(taskId, JobStatusEnum.EXCEPTION_STOP.getCode());
            recordTaskError(taskId, "执行异常: " + e.getMessage());
        } finally {
            runningTasks.remove(taskId);
            terminationFlags.remove(taskId);
        }
    }

    private String verifyTaskResult(String message) {
        // message的格式是 验证data数组中是否存在所有字段都为空的数据，有则不具备，否则具备该能力
        // {
        //  "head": [
        //    {"dataIndex":"name","title":"姓名","key":"name"},
        //    {"dataIndex":"age","title":"年龄","key":"age"}
        //  ],
        //  "data": [
        //    {"name":"John","age":"25"},
        //    {"name":"Jane","age":"30"}
        //  ]
        //}
        JSONObject jsonObject = JSONUtil.parseObj(message);
        JSONArray data = jsonObject.getJSONArray("data");
        if (data.isEmpty())
            return "不具备";
        JSONArray head = jsonObject.getJSONArray("head");
        List<String> fields = head.stream().map(o -> ((JSONObject)o).getStr("dataIndex")).collect(Collectors.toList());
        return data.stream().anyMatch(o -> {
            JSONObject obj = (JSONObject)o;
            return fields.stream().allMatch(f -> obj.getStr(f) == null);
        }) ? "不具备" : "具备";
    }

    /**
     * 执行任务步骤
     */
    private StepMsgDTO executeTaskSteps(WorkflowTask task, Long executionId) {
        Long taskId = task.getId();
        List<TaskStep> steps = taskStepService.selectTaskStepByTaskId(taskId);
        if (CollUtil.isEmpty(steps)) {
            log.warn("任务没有配置步骤，任务ID: {}", taskId);
            return new StepMsgDTO(Boolean.FALSE, "任务没有配置步骤");
        }

        // 对接产品所有接口需要终端填写的参数
        List<IntegrationInterfaceVO> commonParams = JSONUtil.toList(task.getTaskConfig(), IntegrationInterfaceVO.class);

        // 根据任务类型选择不同的工作流执行策略
        if (Objects.equals(task.getTaskType(), 1)) {
            // 能力验证工作流：支持早期终止
            log.info("执行能力验证工作流，任务ID: {}", taskId);
            return executeCapabilityVerificationWorkflow(task, executionId, steps, commonParams);
        } else {
            // 结果查询工作流：原有逻辑保持不变
            log.info("执行结果查询工作流，任务ID: {}", taskId);
            return executeResultQueryWorkflow(task, executionId, steps, commonParams);
        }
    }

    /**
     * 执行能力验证工作流（taskType = 1）
     * 支持早期终止：当任何一步的筛选结果包含数据时，立即返回成功
     */
    private StepMsgDTO executeCapabilityVerificationWorkflow(WorkflowTask task, Long executionId,
                                                           List<TaskStep> steps,
                                                           List<IntegrationInterfaceVO> commonParams) {
        Long taskId = task.getId();
        String previousOutput = null;
        int completedSteps = 0;
        int failedSteps = 0;
        int skippedSteps = 0;

        log.info("开始执行能力验证工作流，任务ID: {}, 总步骤数: {}", taskId, steps.size());

        for (int i = 0; i < steps.size(); i++) {
            // 检查是否需要终止
            if (terminationFlags.getOrDefault(taskId, false)) {
                log.info("任务被手动终止，任务ID: {}, 步骤ID: {}", taskId, steps.get(i).getId());
                updateTaskStatus(taskId, JobStatusEnum.MANUAL_STOP.getCode());
                return new StepMsgDTO(Boolean.FALSE, "任务被手动终止");
            }

            // 更新当前步骤
            updateTaskProgress(taskId, steps.get(i).getStepOrder(),
                calculateProgress(steps.get(i).getStepOrder(), steps.size()));
            taskExecutionService.updateExecutionProgress(executionId, steps.get(i).getStepOrder(),
                calculateProgress(steps.get(i).getStepOrder(), steps.size()),
                completedSteps, failedSteps, skippedSteps);

            Long interfaceId = steps.get(i).getInterfaceId();
            IntegrationInterfaceVO interfaceParam = commonParams.stream().filter(p ->
                    Objects.equal(interfaceId, p.getInterfaceId())).findFirst().orElse(null);

            TaskStep nextStep = null;
            if (i < steps.size() - 1) {
                nextStep = steps.get(i + 1);
            }

            // 执行步骤
            StepMsgDTO stepSuccess = taskStepService.executeStep(steps.get(i), nextStep, previousOutput, interfaceParam);

            if (stepSuccess.getSuccess()) {
                completedSteps++;
                // 获取步骤输出作为下一步的输入
                TaskStep updatedStep = taskStepService.selectTaskStepByStepId(steps.get(i).getId());
                previousOutput = updatedStep.getOutputData();

                // 能力验证关键逻辑：检查当前步骤是否验证了能力
                if (checkCapabilityVerified(updatedStep, interfaceParam)) {
                    log.info("能力验证成功，在第{}步检测到能力，任务ID: {}, 步骤ID: {}",
                            i + 1, taskId, steps.get(i).getId());

                    // 更新最终进度为100%
                    updateTaskProgress(taskId, steps.size(), 100);
                    taskExecutionService.updateExecutionProgress(executionId, steps.size(), 100,
                        completedSteps, failedSteps, skippedSteps);

                    // 返回成功，包含验证成功的数据
                    return new StepMsgDTO(Boolean.TRUE, updatedStep.getOutputData());
                }

                log.debug("第{}步未检测到能力，继续下一步，任务ID: {}", i + 1, taskId);
            } else {
                failedSteps++;
                // 检查是否必须成功
                if (Objects.equal(steps.get(i).getRequired(), 1)) {
                    log.error("必需步骤执行失败，任务终止，任务ID: {}, 步骤ID: {}", taskId, steps.get(i).getId());
                    return new StepMsgDTO(Boolean.FALSE, stepSuccess.getMessage());
                }
                // 检查是否继续执行
                if (Objects.equal(steps.get(i).getContinueOnFailure(), 0)) {
                    log.error("步骤失败且不允许继续，任务终止，任务ID: {}, 步骤ID: {}", taskId, steps.get(i).getId());
                    return new StepMsgDTO(Boolean.FALSE, stepSuccess.getMessage());
                }
                // 跳过后续处理
                skippedSteps++;
                log.warn("第{}步执行失败但允许继续，任务ID: {}, 错误: {}", i + 1, taskId, stepSuccess.getMessage());
            }
        }

        // 所有步骤都执行完毕但未检测到能力
        log.info("能力验证工作流执行完毕，未检测到能力，任务ID: {}", taskId);

        // 更新最终进度
        updateTaskProgress(taskId, steps.size(), 100);
        taskExecutionService.updateExecutionProgress(executionId, steps.size(), 100,
            completedSteps, failedSteps, skippedSteps);

        // 返回成功但表示未检测到能力
        return new StepMsgDTO(Boolean.TRUE, "{}");
    }

    /**
     * 计算进度百分比
     */
    private Integer calculateProgress(Integer currentStep, Integer totalSteps) {
        if (totalSteps == null || totalSteps == 0) {
            return 0;
        }
        return (int) Math.round((double) currentStep / totalSteps * 100);
    }

    /**
     * 获取当前用户名
     */
    private String getUsername() {
        try {
            return SecurityUtils.getUsername();
        } catch (Exception e) {
            return "system";
        }
    }

    /**
     * 获取当前用户ID
     */
    private Long getUserId() {
        try {
            return SecurityUtils.getUserId();
        } catch (Exception e) {
            return 1L;
        }
    }

    /**
     * 终止正在执行的任务
     *
     * @param taskId 任务ID
     * @param reason 终止原因
     * @return 结果
     */
    @Override
    public boolean terminateTask(Long taskId, String reason) {
        log.info("终止任务，任务ID: {}, 原因: {}", taskId, reason);

        if (!canTerminateTask(taskId)) {
            log.warn("任务不能终止，任务ID: {}", taskId);
            return false;
        }

        // 设置终止标志
        terminationFlags.put(taskId, true);

        // 取消正在执行的任务
        CompletableFuture<Void> runningTask = runningTasks.get(taskId);
        if (runningTask != null) {
            runningTask.cancel(true);
        }

        // 更新任务状态
        updateTaskStatus(taskId, JobStatusEnum.MANUAL_STOP.getCode());
        recordTaskError(taskId, "任务被手动终止: " + reason);

        // 更新执行记录
        TaskExecution latestExecution = taskExecutionService.getLatestTaskExecution(taskId);
        if (latestExecution != null) {
            taskExecutionService.terminateExecution(latestExecution.getExecutionId(), reason);
        }

        return true;
    }

    /**
     * 重启失败或终止的任务
     *
     * @param taskId 任务ID
     */
    @Override
    public void restartTask(Long taskId) {
        log.info("重启任务，任务ID: {}", taskId);

        // 重置任务状态
        updateTaskStatus(taskId, JobStatusEnum.RUNNING.getCode());
        if (!needClearTaskContext(taskId)) {
            return;
        }

        log.info("开始清理任务上下文，任务ID: {}", taskId);
        updateTaskProgress(taskId, 0, 0);

        // 重置所有步骤状态
        List<TaskStep> steps = taskStepService.selectTaskStepByTaskId(taskId);
        for (TaskStep step : steps) {
            taskStepService.resetStepToPending(step.getId());
        }

        // 清除错误信息
        baseMapper.updateWorkflowTaskError(taskId, null, getUsername());

        log.info("任务重启成功，任务ID: {}", taskId);
    }

    /**
     * 检查任务是否可以执行
     *
     * @param taskId 任务ID
     * @return 是否可以执行
     */
    @Override
    public boolean canExecuteTask(Long taskId) {
        WorkflowTask task = baseMapper.selectWorkflowTaskByTaskId(taskId);
        if (task == null) {
            return false;
        }
        JobStatusEnum status = JobStatusEnum.getType(task.getStatus());
        return status == JobStatusEnum.UN_START;
    }

    /**
     * 检查任务是否可以终止
     *
     * @param taskId 任务ID
     * @return 是否可以终止
     */
    @Override
    public boolean canTerminateTask(Long taskId) {
        WorkflowTask task = baseMapper.selectWorkflowTaskByTaskId(taskId);
        if (task == null) {
            return false;
        }
        JobStatusEnum status = JobStatusEnum.getType(task.getStatus());
        return status == JobStatusEnum.RUNNING;
    }

    /**
     * 检查任务是否可以重启
     *
     * @param taskId 任务ID
     * @return 是否可以重启
     */
    @Override
    public boolean needClearTaskContext(Long taskId) {
        WorkflowTask task = baseMapper.selectWorkflowTaskByTaskId(taskId);
        if (task == null) {
            return false;
        }

        JobStatusEnum status = JobStatusEnum.getType(task.getStatus());
        return status != JobStatusEnum.UN_START;
    }

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 新状态
     * @return 结果
     */
    @Override
    public boolean updateTaskStatus(Long taskId, Integer status) {
        return baseMapper.updateWorkflowTaskStatus(taskId, status, getUsername()) > 0;
    }

    /**
     * 更新任务进度
     *
     * @param taskId 任务ID
     * @param currentStep 当前步骤
     * @param progressPercentage 进度百分比
     * @return 结果
     */
    @Override
    public boolean updateTaskProgress(Long taskId, Integer currentStep, Integer progressPercentage) {
        return baseMapper.updateWorkflowTaskProgress(taskId, currentStep, progressPercentage, getUsername()) > 0;
    }

    /**
     * 记录任务错误
     *
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     * @return 结果
     */
    @Override
    public boolean recordTaskError(Long taskId, String errorMessage) {
        return baseMapper.updateWorkflowTaskError(taskId, errorMessage, getUsername()) > 0;
    }

    @Override
    public WorkflowDetailVO detail(Long taskId) {
        WorkflowTask workflowTask = baseMapper.selectById(taskId);
        WorkflowDetailVO vo = new WorkflowDetailVO();
        vo.setId(workflowTask.getId());
        vo.setName(workflowTask.getName());
        vo.setTaskType(workflowTask.getTaskType());
        vo.setProductId(workflowTask.getProductId());
        vo.setProductName(workflowTask.getProductName());
        vo.setTaskConfig(JSONUtil.toList(workflowTask.getTaskConfig(), IntegrationInterfaceVO.class));
        return vo;
    }
}
