package com.dcas.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.domain.entity.ApiIntegratedProduct;
import com.dcas.common.domain.entity.ApiInterface;
import com.dcas.common.domain.entity.ApiRelease;
import com.dcas.common.domain.entity.Tag;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.mapper.ApiIntegratedProductMapper;
import com.dcas.common.mapper.ApiInterfaceMapper;
import com.dcas.common.mapper.ApiReleaseMapper;
import com.dcas.common.mapper.TagMapper;
import com.dcas.common.model.other.InParam;
import com.dcas.common.model.other.KeyValue;
import com.dcas.common.model.other.OptionSelect;
import com.dcas.common.model.other.OutParam;
import com.dcas.common.model.vo.ApiReleaseVO;
import com.dcas.common.model.vo.IntegrationFormFieldVO;
import com.dcas.common.model.vo.IntegrationInterfaceVO;
import com.dcas.common.model.vo.ProductCapabilityTreeVO;
import com.dcas.common.utils.StringUtils;
import com.dcas.system.service.IApiInterfaceService;
import com.dcas.system.service.IApiReleaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * API版本记录服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApiReleaseServiceImpl extends ServiceImpl<ApiReleaseMapper, ApiRelease> implements IApiReleaseService {
    private final TagMapper tagMapper;
    private final ApiInterfaceMapper interfaceMapper;
    private final IApiInterfaceService apiInterfaceService;
    private final ApiIntegratedProductMapper apiIntegratedProductMapper;

    @Override
    @SchemaSwitch
    public List<ProductCapabilityTreeVO> getProductCapabilityTree() {
        List<ApiRelease> apiReleases = baseMapper.selectApiReleaseList();
        return buildTree(apiReleases);
    }

    @Override
    @SchemaSwitch
    public List<IntegrationInterfaceVO> getProductsByReleaseId(Long productId, Integer taskType) {
        log.info("开始查询对接产品ID为{}的接口参数配置", productId);
        ApiIntegratedProduct apiIntegratedProduct = apiIntegratedProductMapper.selectById(productId);
        if (Objects.isNull(apiIntegratedProduct)) {
            throw new ServiceException("对接产品不存在");
        }
        Long releaseId = apiIntegratedProduct.getReleaseId();
        List<IntegrationInterfaceVO> result = new ArrayList<>();
        ApiRelease apiRelease = baseMapper.selectById(releaseId);
        if (Objects.isNull(apiRelease))
            throw new ServiceException("产品对接类型不存在");

        // 1. 根据release_id查询api_interface表中的数据
        List<ApiInterface> interfaces = apiInterfaceService.getByReleaseId(releaseId, taskType);
        if (CollUtil.isEmpty(interfaces)) {
            log.warn("未找到产品类型ID为{}的接口数据", releaseId);
            return result;
        }

        // 3. 遍历所有接口，解析req_params字段
        for (ApiInterface apiInterface : interfaces) {
            IntegrationInterfaceVO interfaceVO = new IntegrationInterfaceVO();
            interfaceVO.setInterfaceId(apiInterface.getId());
            interfaceVO.setApiName(apiInterface.getApiName());
            interfaceVO.setApiMethod(apiInterface.getApiMethod());
            interfaceVO.setApiPath(apiInterface.getApiPath());

            String reqParams = apiInterface.getReqParams();
            if (StringUtils.isEmpty(reqParams)) {
                continue;
            }

            try {
                // 解析入参参数 筛选出需要终端输入类型的参数
                InParam inParam = JSONUtil.toBean(reqParams, InParam.class);
                List<IntegrationFormFieldVO> fieldVOList = inParam.getColumnList().stream().filter(c ->
                        Objects.nonNull(c.getType()) && c.getType() == 1).map(this::convertInParamToFormField).collect(Collectors.toList());

                // 解析出参参数 筛选出参数为筛选条件的参数
                OutParam outParam = JSONUtil.toBean(apiInterface.getReqResult(), OutParam.class);
                fieldVOList.addAll(outParam.getColumnList().stream().filter(o ->
                        Objects.nonNull(o.getRequired()) && o.getRequired() == 2).map(this::convertOutParamToFormField).collect(Collectors.toList()));

                interfaceVO.setConfigParams(fieldVOList);
                if (CollUtil.isEmpty(interfaceVO.getConfigParams()))
                    continue;
                result.add(interfaceVO);
            } catch (Exception e) {
                log.error("解析接口{}的参数失败: {}", apiInterface.getApiName(), e.getMessage());
            }
        }

        return result;
    }

    private IntegrationFormFieldVO convertOutParamToFormField(OutParam.ColumnParam columnParam) {
        IntegrationFormFieldVO fieldVO = new IntegrationFormFieldVO();
        // 设置基本字段
        fieldVO.setName(columnParam.getColumnName());
        fieldVO.setLabel(StringUtils.isNotEmpty(columnParam.getColumnComment()) ?
                columnParam.getColumnComment() : columnParam.getColumnName());
        fieldVO.setValue(columnParam.getValue());
        fieldVO.setRequired(true); // type=1的参数默认为必填
        fieldVO.setColumnType(columnParam.getColumnType());
        fieldVO.setLevel(columnParam.getLevel());
        fieldVO.setLocation(columnParam.getLocation());
        fieldVO.setType(getTypeByColumnType(columnParam.getColumnType()));
        fieldVO.setParamType(2);
        fieldVO.setDisplayName(columnParam.getDisplayName());
        fieldVO.setDataRequired(columnParam.getRequired());
        return fieldVO;
    }

    private String getTypeByColumnType(String columnType) {
        if (StringUtils.isEmpty(columnType)) {
            return "text";
        }
        switch (columnType.toLowerCase()) {
            case "integer":
            case "int":
            case "double":
            case "float":
                return "number";
            case "boolean":
                return "boolean";
            default:
                return "text";
        }
    }

    @Override
    @SchemaSwitch
    public List<ApiReleaseVO> queryApiReleaseList() {
        List<ApiRelease> apiReleases = baseMapper.selectApiReleaseList();
        return apiReleases.stream().map(a -> {
            ApiReleaseVO vo = new ApiReleaseVO();
            BeanUtil.copyProperties(a, vo);
            vo.setApiInterfaces(interfaceMapper.selectByReleaseId(a.getId()));
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    @SchemaSwitch
    public List<IntegrationFormFieldVO> getProductHeader(Long releaseId) {
        ApiRelease apiRelease = baseMapper.selectById(releaseId);
        if (Objects.isNull(apiRelease))
            throw new ServiceException("产品对接类型不存在");
        Map<Long, String> tagMap = tagMapper.selectList(new QueryWrapper<Tag>().eq("status", 0))
                .stream().collect(Collectors.toMap(Tag::getId, Tag::getName));
        return StrUtil.split(apiRelease.getAuth(), StrUtil.COMMA).stream().map(auth -> {
            IntegrationFormFieldVO fieldVO = new IntegrationFormFieldVO();
            String name = tagMap.get(Long.parseLong(auth));
            fieldVO.setName(name);
            fieldVO.setLabel(name);
            fieldVO.setType("password");
            fieldVO.setColumnType("STRING");
            fieldVO.setRequired(true);
            fieldVO.setParamType(0);
            return fieldVO;
        }).collect(Collectors.toList());
    }

    private List<ProductCapabilityTreeVO> buildTree(List<ApiRelease> apiReleases) {
        return apiReleases.stream().collect(Collectors.groupingBy(p ->
                BeanUtil.copyProperties(p, ProductCapabilityTreeVO.class))).entrySet().stream().map(e -> {
            ProductCapabilityTreeVO vo = new ProductCapabilityTreeVO();
            ProductCapabilityTreeVO key = e.getKey();
            vo.setCapabilityName(key.getCapabilityName());
            vo.setCapability(key.getCapability());
            vo.setProducts(e.getValue().stream().map(apiRelease -> {
                ProductCapabilityTreeVO.ProductInfoVO productInfoVO = new ProductCapabilityTreeVO.ProductInfoVO();
                productInfoVO.setReleaseId(apiRelease.getId());
                productInfoVO.setProductName(apiRelease.getName());
                return productInfoVO;
            }).collect(Collectors.toList()));
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 将InParam.ColumnParam转换为IntegrationFormFieldVO
     */
    private IntegrationFormFieldVO convertInParamToFormField(InParam.ColumnParam columnParam) {
        IntegrationFormFieldVO fieldVO = new IntegrationFormFieldVO();

        // 设置基本字段
        fieldVO.setName(columnParam.getColumnName());
        fieldVO.setLabel(StringUtils.isNotEmpty(columnParam.getColumnComment()) ?
                        columnParam.getColumnComment() : columnParam.getColumnName());
        fieldVO.setRequired(true); // type=1的参数默认为必填
        fieldVO.setColumnType(columnParam.getColumnType());
        fieldVO.setLevel(columnParam.getLevel());
        fieldVO.setLocation(columnParam.getLocation());
        if (columnParam.getConfigType() == 2) {
            fieldVO.setType("password");
        } else {
            if (columnParam.getConfigType() == 3 || columnParam.getConfigType() == 4) {
                fieldVO.setType("select");
                fieldVO.setShowArrow(true);
                fieldVO.setShowSearch(true);
                fieldVO.setMode(columnParam.getConfigType() == 3 ? "single" : "multiple");
                fieldVO.setOptions(transformOptions(columnParam.getConfigValue()));
            } else {
                fieldVO.setType(getTypeByColumnType(columnParam.getColumnType()));
            }
        }
        fieldVO.setDataType(columnParam.getType());
        // type=0的参数返回默认值，否则为null
        fieldVO.setValue(Objects.equals(columnParam.getType(), 0) ? columnParam.getValue() : null);
        fieldVO.setParamType(1);
        return fieldVO;
    }

    private List<OptionSelect<String>> transformOptions(List<KeyValue> configValue) {
        return configValue.stream().map(v -> new OptionSelect<>(v.getKey().toString(), v.getValue())).collect(Collectors.toList());
    }
}
