package com.dcas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcas.common.domain.entity.ApiInterface;
import com.dcas.common.model.vo.ProductIntegrationVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * API接口Mapper接口
 *
 * <AUTHOR>
 */
public interface ApiInterfaceMapper extends BaseMapper<ApiInterface> {
    
    /**
     * 根据API版本记录ID查询接口列表
     *
     * @param releaseId API版本记录ID
     * @return 接口列表
     */
    List<ApiInterface> selectByReleaseId(@Param("releaseId") Long releaseId);

    List<ApiInterface> selectByReleaseIdAndTaskType(@Param("releaseId") Long releaseId, @Param("taskType") Integer taskType);
}
